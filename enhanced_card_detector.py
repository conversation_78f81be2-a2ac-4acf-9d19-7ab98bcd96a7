import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import math

class EnhancedCardDetector:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced Card Detector - Optimized Preprocessing")
        self.root.geometry("1600x1000")
        
        # Variables
        self.image = None
        self.original_image = None
        self.detections = []
        self.current_detection_index = 0
        self.display_image = None
        self.processed_image = None
        self.preprocessing_steps = []
        
        # Create GUI
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control frame (top)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection
        ttk.Button(control_frame, text="Select Image", command=self.select_image).pack(side=tk.LEFT, padx=(0, 10))
        
        # Quick test buttons for test images
        test_frame = ttk.LabelFrame(control_frame, text="Quick Test")
        test_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        test_buttons_frame = ttk.Frame(test_frame)
        test_buttons_frame.pack(padx=5, pady=2)
        
        for i in range(1, 9):
            ttk.Button(test_buttons_frame, text=f"T{i}", width=3,
                      command=lambda x=i: self.load_test_image(x)).pack(side=tk.LEFT, padx=1)
        
        # Detection method selection
        ttk.Label(control_frame, text="Method:").pack(side=tk.LEFT, padx=(10, 5))
        self.method_var = tk.StringVar(value="Contrast Enhanced")
        method_combo = ttk.Combobox(control_frame, textvariable=self.method_var, width=18, state="readonly")
        method_combo['values'] = ("Contrast Enhanced", "Color Separation", "Edge Enhancement", "Adaptive Contrast", "Multi-Scale")
        method_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection button
        ttk.Button(control_frame, text="Detect Cards", command=self.detect_cards).pack(side=tk.LEFT, padx=(5, 10))
        
        # Navigation buttons
        self.prev_btn = ttk.Button(control_frame, text="Previous", command=self.prev_detection, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.next_btn = ttk.Button(control_frame, text="Next", command=self.next_detection, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection counter
        self.counter_label = ttk.Label(control_frame, text="No detections")
        self.counter_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Crop button
        self.crop_btn = ttk.Button(control_frame, text="Crop Current", command=self.crop_current, state=tk.DISABLED)
        self.crop_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Parameters frame
        params_frame = ttk.LabelFrame(control_frame, text="Enhancement Parameters")
        params_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Parameters row 1
        params_row1 = ttk.Frame(params_frame)
        params_row1.pack(padx=5, pady=2)
        
        ttk.Label(params_row1, text="Contrast:").pack(side=tk.LEFT, padx=(0, 2))
        self.contrast_var = tk.StringVar(value="2.0")
        ttk.Entry(params_row1, textvariable=self.contrast_var, width=6).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row1, text="Brightness:").pack(side=tk.LEFT, padx=(5, 2))
        self.brightness_var = tk.StringVar(value="30")
        ttk.Entry(params_row1, textvariable=self.brightness_var, width=6).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row1, text="Min Area:").pack(side=tk.LEFT, padx=(5, 2))
        self.min_area_var = tk.StringVar(value="5000")
        ttk.Entry(params_row1, textvariable=self.min_area_var, width=8).pack(side=tk.LEFT)
        
        # Parameters row 2
        params_row2 = ttk.Frame(params_frame)
        params_row2.pack(padx=5, pady=2)
        
        ttk.Label(params_row2, text="Gamma:").pack(side=tk.LEFT, padx=(0, 2))
        self.gamma_var = tk.StringVar(value="1.2")
        ttk.Entry(params_row2, textvariable=self.gamma_var, width=6).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row2, text="Blur:").pack(side=tk.LEFT, padx=(5, 2))
        self.blur_var = tk.StringVar(value="3")
        ttk.Entry(params_row2, textvariable=self.blur_var, width=6).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row2, text="Aspect:").pack(side=tk.LEFT, padx=(5, 2))
        self.aspect_ratio_var = tk.StringVar(value="0.3")
        ttk.Entry(params_row2, textvariable=self.aspect_ratio_var, width=6).pack(side=tk.LEFT)
        
        # Parameters row 3 - Display options
        params_row3 = ttk.Frame(params_frame)
        params_row3.pack(padx=5, pady=2)
        
        self.show_preprocessing_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(params_row3, text="Show Steps", 
                       variable=self.show_preprocessing_var,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 5))
        
        self.show_all_detections_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_row3, text="Show All", 
                       variable=self.show_all_detections_var,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 5))
        
        self.auto_enhance_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_row3, text="Auto Enhance", 
                       variable=self.auto_enhance_var).pack(side=tk.LEFT)
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Image display frame (left)
        image_frame = ttk.LabelFrame(content_frame, text="Image Display")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Canvas for image display
        self.canvas = tk.Canvas(image_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Info frame (right)
        info_frame = ttk.LabelFrame(content_frame, text="Detection & Processing Info")
        info_frame.pack(side=tk.RIGHT, fill=tk.Y)
        info_frame.configure(width=400)
        info_frame.pack_propagate(False)
        
        # Info text widget
        self.info_text = tk.Text(info_frame, width=45, height=40, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def load_test_image(self, test_num):
        """Load a test image from the test images folder"""
        file_path = f"test images/test{test_num}.jpg"
        try:
            self.original_image = cv2.imread(file_path)
            if self.original_image is None:
                messagebox.showerror("Error", f"Could not load test image {test_num}")
                return
            
            self.image = self.original_image.copy()
            self.detections = []
            self.current_detection_index = 0
            self.update_ui_state()
            self.display_image_on_canvas(self.original_image)
            
            # Auto-detect if enabled
            if hasattr(self, 'auto_enhance_var') and self.auto_enhance_var.get():
                self.detect_cards()
                
        except Exception as e:
            messagebox.showerror("Error", f"Error loading test image {test_num}: {str(e)}")
    
    def select_image(self):
        file_path = filedialog.askopenfilename(
            title="Select an image",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.original_image = cv2.imread(file_path)
                if self.original_image is None:
                    messagebox.showerror("Error", "Could not load the image file.")
                    return
                
                self.image = self.original_image.copy()
                self.detections = []
                self.current_detection_index = 0
                self.update_ui_state()
                self.display_image_on_canvas(self.original_image)
                
            except Exception as e:
                messagebox.showerror("Error", f"Error loading image: {str(e)}")
    
    def enhance_contrast_brightness(self, image, contrast=1.0, brightness=0):
        """Apply contrast and brightness enhancement"""
        enhanced = cv2.convertScaleAbs(image, alpha=contrast, beta=brightness)
        return enhanced
    
    def apply_gamma_correction(self, image, gamma=1.0):
        """Apply gamma correction"""
        inv_gamma = 1.0 / gamma
        table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
        return cv2.LUT(image, table)
    
    def enhance_edges_unsharp_mask(self, image):
        """Apply unsharp masking to enhance edges"""
        gaussian = cv2.GaussianBlur(image, (0, 0), 2.0)
        unsharp_mask = cv2.addWeighted(image, 1.5, gaussian, -0.5, 0)
        return unsharp_mask
    
    def color_based_enhancement(self, image):
        """Enhance based on color differences"""
        # Convert to LAB color space for better color separation
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        l = clahe.apply(l)
        
        # Enhance A and B channels
        a = cv2.convertScaleAbs(a, alpha=1.2, beta=10)
        b = cv2.convertScaleAbs(b, alpha=1.2, beta=10)
        
        # Merge and convert back
        enhanced_lab = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        
        return enhanced
    
    def detect_cards(self):
        if self.original_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return
        
        method = self.method_var.get()
        
        if method == "Contrast Enhanced":
            self.detect_with_contrast_enhancement()
        elif method == "Color Separation":
            self.detect_with_color_separation()
        elif method == "Edge Enhancement":
            self.detect_with_edge_enhancement()
        elif method == "Adaptive Contrast":
            self.detect_with_adaptive_contrast()
        elif method == "Multi-Scale":
            self.detect_with_multiscale()
        
        self.current_detection_index = 0
        self.update_ui_state()
        self.update_display()

    def detect_with_contrast_enhancement(self):
        """Aggressive contrast and brightness enhancement for card detection"""
        try:
            contrast = float(self.contrast_var.get())
            brightness = float(self.brightness_var.get())
            gamma = float(self.gamma_var.get())
            blur_size = int(self.blur_var.get())
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            contrast, brightness, gamma = 2.0, 30, 1.2
            blur_size, min_area, min_aspect = 3, 5000, 0.3

        self.preprocessing_steps = []

        # Step 1: Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        self.preprocessing_steps.append(("1. Grayscale", gray.copy()))

        # Step 2: Apply gamma correction first
        gamma_corrected = self.apply_gamma_correction(gray, gamma)
        self.preprocessing_steps.append(("2. Gamma Correction", gamma_corrected.copy()))

        # Step 3: Aggressive contrast and brightness
        enhanced = self.enhance_contrast_brightness(gamma_corrected, contrast, brightness)
        self.preprocessing_steps.append(("3. Contrast/Brightness", enhanced.copy()))

        # Step 4: CLAHE for local contrast
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(8,8))
        clahe_enhanced = clahe.apply(enhanced)
        self.preprocessing_steps.append(("4. CLAHE Enhanced", clahe_enhanced.copy()))

        # Step 5: Unsharp masking for edge enhancement
        unsharp = self.enhance_edges_unsharp_mask(clahe_enhanced)
        self.preprocessing_steps.append(("5. Unsharp Mask", unsharp.copy()))

        # Step 6: Bilateral filter to smooth while preserving edges
        if blur_size % 2 == 0:
            blur_size += 1
        bilateral = cv2.bilateralFilter(unsharp, 9, 80, 80)
        self.preprocessing_steps.append(("6. Bilateral Filter", bilateral.copy()))

        # Step 7: Multiple edge detection with different thresholds
        edges1 = cv2.Canny(bilateral, 20, 60)
        edges2 = cv2.Canny(bilateral, 50, 150)
        edges3 = cv2.Canny(bilateral, 100, 200)

        # Combine all edge detections
        edges_combined = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))
        self.preprocessing_steps.append(("7. Multi-Threshold Canny", edges_combined.copy()))

        # Step 8: Morphological operations to close gaps
        kernel_small = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))

        # Close small gaps
        closed = cv2.morphologyEx(edges_combined, cv2.MORPH_CLOSE, kernel_small)
        # Dilate to connect nearby edges
        dilated = cv2.dilate(closed, kernel_small, iterations=2)
        # Close larger gaps
        final_edges = cv2.morphologyEx(dilated, cv2.MORPH_CLOSE, kernel_large)

        self.preprocessing_steps.append(("8. Morphological Ops", final_edges.copy()))
        self.processed_image = final_edges

        # Find contours
        contours, _ = cv2.findContours(final_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            # Additional filtering for card-like shapes
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue

            # Circularity test (cards should be somewhat rectangular)
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            if circularity > 0.85:  # Too circular, probably not a card
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'circularity': circularity,
                'method': 'Contrast Enhanced'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def detect_with_color_separation(self):
        """Use color space separation to enhance card detection"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            min_area, min_aspect = 5000, 0.3

        self.preprocessing_steps = []

        # Step 1: Color enhancement
        color_enhanced = self.color_based_enhancement(self.original_image)
        self.preprocessing_steps.append(("1. Color Enhanced", color_enhanced.copy()))

        # Step 2: Convert to multiple color spaces and find best contrast
        gray = cv2.cvtColor(color_enhanced, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(color_enhanced, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(color_enhanced, cv2.COLOR_BGR2LAB)

        # Use L channel from LAB (often gives best contrast)
        l_channel = lab[:,:,0]
        self.preprocessing_steps.append(("2. LAB L-Channel", l_channel.copy()))

        # Step 3: Adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        equalized = clahe.apply(l_channel)
        self.preprocessing_steps.append(("3. Adaptive Histogram", equalized.copy()))

        # Step 4: Edge detection on enhanced image
        edges = cv2.Canny(equalized, 30, 100)

        # Step 5: Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        self.preprocessing_steps.append(("4. Final Edges", closed.copy()))
        self.processed_image = closed

        # Find contours
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'method': 'Color Separation'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def detect_with_edge_enhancement(self):
        """Focus on aggressive edge enhancement"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
            contrast = float(self.contrast_var.get())
        except ValueError:
            min_area, min_aspect, contrast = 5000, 0.3, 2.0

        self.preprocessing_steps = []

        # Step 1: Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)

        # Step 2: Extreme contrast enhancement
        enhanced = cv2.convertScaleAbs(gray, alpha=contrast, beta=50)
        self.preprocessing_steps.append(("1. High Contrast", enhanced.copy()))

        # Step 3: Multiple edge detection approaches
        # Sobel edges
        sobelx = cv2.Sobel(enhanced, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(enhanced, cv2.CV_64F, 0, 1, ksize=3)
        sobel_combined = np.sqrt(sobelx**2 + sobely**2)
        sobel_edges = np.uint8(sobel_combined / sobel_combined.max() * 255)

        # Laplacian edges
        laplacian = cv2.Laplacian(enhanced, cv2.CV_64F)
        laplacian_edges = np.uint8(np.absolute(laplacian))

        # Canny edges with multiple thresholds
        canny1 = cv2.Canny(enhanced, 30, 90)
        canny2 = cv2.Canny(enhanced, 50, 150)

        # Combine all edge detections
        all_edges = cv2.bitwise_or(sobel_edges, cv2.bitwise_or(laplacian_edges, cv2.bitwise_or(canny1, canny2)))
        self.preprocessing_steps.append(("2. Combined Edges", all_edges.copy()))

        # Step 4: Aggressive morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        closed = cv2.morphologyEx(all_edges, cv2.MORPH_CLOSE, kernel)

        # Fill holes
        kernel_fill = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))
        filled = cv2.morphologyEx(closed, cv2.MORPH_CLOSE, kernel_fill)

        self.preprocessing_steps.append(("3. Morphological Fill", filled.copy()))
        self.processed_image = filled

        # Find contours
        contours, _ = cv2.findContours(filled, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'method': 'Edge Enhancement'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def detect_with_adaptive_contrast(self):
        """Adaptive contrast enhancement based on local statistics"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            min_area, min_aspect = 5000, 0.3

        self.preprocessing_steps = []

        # Step 1: Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)

        # Step 2: Adaptive histogram equalization with multiple tile sizes
        clahe_small = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(4,4))
        clahe_large = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(16,16))

        enhanced_small = clahe_small.apply(gray)
        enhanced_large = clahe_large.apply(gray)

        # Combine both enhancements
        combined = cv2.addWeighted(enhanced_small, 0.7, enhanced_large, 0.3, 0)
        self.preprocessing_steps.append(("1. Multi-Scale CLAHE", combined.copy()))

        # Step 3: Local contrast enhancement
        # Create a mask for high-contrast areas
        blurred = cv2.GaussianBlur(combined, (21, 21), 0)
        contrast_mask = cv2.absdiff(combined, blurred)

        # Enhance high-contrast areas more
        enhanced = np.where(contrast_mask > 30,
                           cv2.convertScaleAbs(combined, alpha=2.5, beta=20),
                           cv2.convertScaleAbs(combined, alpha=1.5, beta=10))

        self.preprocessing_steps.append(("2. Local Enhancement", enhanced.copy()))

        # Step 4: Edge detection
        edges = cv2.Canny(enhanced, 40, 120)

        # Step 5: Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        self.preprocessing_steps.append(("3. Final Edges", closed.copy()))
        self.processed_image = closed

        # Find contours
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'method': 'Adaptive Contrast'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def detect_with_multiscale(self):
        """Multi-scale detection combining results from different scales"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            min_area, min_aspect = 5000, 0.3

        self.preprocessing_steps = []

        # Step 1: Create multiple scales of the image
        original = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)

        # Scale 1: Original size with high contrast
        scale1 = cv2.convertScaleAbs(original, alpha=3.0, beta=40)

        # Scale 2: Downsampled and upsampled (removes fine details)
        small = cv2.resize(original, None, fx=0.5, fy=0.5)
        scale2 = cv2.resize(small, (original.shape[1], original.shape[0]))
        scale2 = cv2.convertScaleAbs(scale2, alpha=2.5, beta=30)

        # Scale 3: Heavily blurred original (emphasizes large structures)
        scale3 = cv2.GaussianBlur(original, (15, 15), 0)
        scale3 = cv2.convertScaleAbs(scale3, alpha=2.0, beta=20)

        self.preprocessing_steps.append(("1. Multi-Scale Prep", scale1.copy()))

        # Step 2: Edge detection on each scale
        edges1 = cv2.Canny(scale1, 50, 150)
        edges2 = cv2.Canny(scale2, 30, 100)
        edges3 = cv2.Canny(scale3, 20, 80)

        # Combine all scales
        combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))
        self.preprocessing_steps.append(("2. Multi-Scale Edges", combined_edges.copy()))

        # Step 3: Aggressive morphological operations
        kernel_small = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (9, 9))

        # Close small gaps
        closed = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel_small)
        # Dilate to connect edges
        dilated = cv2.dilate(closed, kernel_small, iterations=3)
        # Close large gaps
        final = cv2.morphologyEx(dilated, cv2.MORPH_CLOSE, kernel_large)

        self.preprocessing_steps.append(("3. Final Processing", final.copy()))
        self.processed_image = final

        # Find contours
        contours, _ = cv2.findContours(final, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            # Additional quality checks
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue

            # Compactness (lower is more rectangular)
            compactness = (perimeter * perimeter) / (4 * np.pi * area)

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'compactness': compactness,
                'method': 'Multi-Scale'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def prev_detection(self):
        if self.detections and self.current_detection_index > 0:
            self.current_detection_index -= 1
            self.update_counter_label()
            self.update_display()

    def next_detection(self):
        if self.detections and self.current_detection_index < len(self.detections) - 1:
            self.current_detection_index += 1
            self.update_counter_label()
            self.update_display()

    def update_ui_state(self):
        if self.detections:
            self.prev_btn.config(state=tk.NORMAL)
            self.next_btn.config(state=tk.NORMAL)
            self.crop_btn.config(state=tk.NORMAL)
            self.update_counter_label()
        else:
            self.prev_btn.config(state=tk.DISABLED)
            self.next_btn.config(state=tk.DISABLED)
            self.crop_btn.config(state=tk.DISABLED)
            self.counter_label.config(text="No detections")

    def update_counter_label(self):
        if self.detections:
            self.counter_label.config(
                text=f"Detection {self.current_detection_index + 1} of {len(self.detections)}"
            )

    def update_display(self):
        if self.original_image is None:
            return

        # Choose what to display
        if self.show_preprocessing_var.get() and self.preprocessing_steps:
            # Show preprocessing steps in a grid
            self.display_preprocessing_steps()
            return

        # Show original image with detections
        display_img = self.original_image.copy()

        if not self.detections:
            self.display_image_on_canvas(display_img)
            self.update_info_panel(None)
            return

        # Draw all detections if enabled
        if self.show_all_detections_var.get():
            for i, detection in enumerate(self.detections):
                color = (128, 128, 128) if i != self.current_detection_index else (0, 0, 255)
                thickness = 1 if i != self.current_detection_index else 3
                cv2.drawContours(display_img, [detection['contour']], -1, color, thickness)

        # Highlight current detection
        if self.detections:
            current_detection = self.detections[self.current_detection_index]
            cv2.drawContours(display_img, [current_detection['contour']], -1, (0, 0, 255), 4)

            # Draw bounding rectangle
            x, y, w, h = current_detection['bbox']
            cv2.rectangle(display_img, (x, y), (x + w, y + h), (255, 0, 0), 3)

            # Draw detection number and confidence
            cv2.putText(display_img, f"#{self.current_detection_index + 1}",
                       (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 3)

        self.display_image_on_canvas(display_img)
        self.update_info_panel(current_detection if self.detections else None)

    def display_preprocessing_steps(self):
        """Display preprocessing steps in a grid"""
        if not self.preprocessing_steps:
            return

        # Create a grid of preprocessing steps
        num_steps = len(self.preprocessing_steps)
        cols = min(3, num_steps)
        rows = (num_steps + cols - 1) // cols

        # Calculate size for each step image
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, self.display_preprocessing_steps)
            return

        step_width = canvas_width // cols
        step_height = canvas_height // rows

        # Create combined image
        combined_img = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)

        for i, (title, img) in enumerate(self.preprocessing_steps):
            row = i // cols
            col = i % cols

            # Resize step image to fit
            if len(img.shape) == 2:  # Grayscale
                img_resized = cv2.resize(img, (step_width-10, step_height-30))
                img_colored = cv2.cvtColor(img_resized, cv2.COLOR_GRAY2BGR)
            else:
                img_colored = cv2.resize(img, (step_width-10, step_height-30))

            # Calculate position
            y_start = row * step_height + 20
            y_end = y_start + img_colored.shape[0]
            x_start = col * step_width + 5
            x_end = x_start + img_colored.shape[1]

            # Place image
            combined_img[y_start:y_end, x_start:x_end] = img_colored

            # Add title
            cv2.putText(combined_img, title, (x_start, row * step_height + 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        self.display_image_on_canvas(combined_img)

    def display_image_on_canvas(self, img):
        # Convert BGR to RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Get canvas dimensions
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, lambda: self.display_image_on_canvas(img))
            return

        # Calculate scaling factor to fit image in canvas
        img_height, img_width = img_rgb.shape[:2]
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # Don't upscale

        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        img_resized = cv2.resize(img_rgb, (new_width, new_height))

        # Convert to PIL Image and then to PhotoImage
        pil_img = Image.fromarray(img_resized)
        self.display_image = ImageTk.PhotoImage(pil_img)

        # Clear canvas and display image
        self.canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        self.canvas.create_image(x, y, anchor=tk.NW, image=self.display_image)

    def update_info_panel(self, detection):
        self.info_text.delete(1.0, tk.END)

        if detection is None:
            if self.preprocessing_steps:
                info = "PREPROCESSING STEPS:\n\n"
                for i, (title, _) in enumerate(self.preprocessing_steps):
                    info += f"{i+1}. {title}\n"
                info += "\nCheck 'Show Steps' to see visual results of each preprocessing step.\n\n"
                info += "ENHANCEMENT TECHNIQUES USED:\n"
                info += "• Gamma correction for brightness\n"
                info += "• Contrast & brightness enhancement\n"
                info += "• CLAHE for local contrast\n"
                info += "• Unsharp masking for edges\n"
                info += "• Multi-threshold Canny detection\n"
                info += "• Morphological operations\n"
                info += "• Color space optimization\n"
            else:
                info = "No detection selected or no detections found.\n\nTry different methods or adjust parameters."
            self.info_text.insert(tk.END, info)
            return

        # Calculate additional properties
        contour = detection['contour']
        area = detection['area']
        x, y, w, h = detection['bbox']

        # Perimeter
        perimeter = cv2.arcLength(contour, True)

        # Minimum enclosing circle
        (cx, cy), radius = cv2.minEnclosingCircle(contour)

        # Extent (ratio of contour area to bounding rectangle area)
        rect_area = w * h
        extent = float(area) / rect_area if rect_area != 0 else 0

        # Solidity (ratio of contour area to convex hull area)
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        solidity = float(area) / hull_area if hull_area != 0 else 0

        info = f"""DETECTION #{self.current_detection_index + 1}
Method: {detection['method']}
Rank: {self.current_detection_index + 1} (by area)

BASIC PROPERTIES:
Area: {area:.0f} pixels
Perimeter: {perimeter:.1f} pixels
Points: {len(contour)}

BOUNDING RECTANGLE:
Position: ({x}, {y})
Size: {w} × {h} pixels
Aspect Ratio: {detection['aspect_ratio']:.3f}

SHAPE ANALYSIS:
Extent: {extent:.3f}
Solidity: {solidity:.3f}
"""

        if 'circularity' in detection:
            info += f"Circularity: {detection['circularity']:.3f}\n"

        if 'compactness' in detection:
            info += f"Compactness: {detection['compactness']:.3f}\n"

        info += f"\nQUALITY INDICATORS:\n"
        info += f"Rectangularity: {extent:.3f} (>0.7 is good)\n"
        info += f"Convexity: {solidity:.3f} (>0.8 is good)\n"

        # Quality assessment
        quality_score = 0
        quality_reasons = []

        if extent > 0.7:
            quality_score += 30
            quality_reasons.append("✓ Good rectangularity")
        else:
            quality_reasons.append("✗ Low rectangularity")

        if solidity > 0.8:
            quality_score += 25
            quality_reasons.append("✓ Good convexity")
        else:
            quality_reasons.append("✗ Low convexity")

        if detection['aspect_ratio'] > 0.4:
            quality_score += 20
            quality_reasons.append("✓ Good aspect ratio")
        else:
            quality_reasons.append("✗ Too elongated")

        if area > 10000:
            quality_score += 25
            quality_reasons.append("✓ Good size")
        else:
            quality_reasons.append("✗ Small size")

        info += f"\nQUALITY SCORE: {quality_score}/100\n"
        for reason in quality_reasons:
            info += f"{reason}\n"

        # Method-specific information
        info += f"\n{detection['method'].upper()} METHOD:\n"
        if detection['method'] == 'Contrast Enhanced':
            info += "• Gamma correction applied\n"
            info += "• High contrast enhancement\n"
            info += "• CLAHE local enhancement\n"
            info += "• Unsharp masking\n"
            info += "• Multi-threshold edges\n"
        elif detection['method'] == 'Color Separation':
            info += "• LAB color space used\n"
            info += "• L-channel optimization\n"
            info += "• Color-based enhancement\n"
        elif detection['method'] == 'Edge Enhancement':
            info += "• Sobel + Laplacian edges\n"
            info += "• Multiple Canny thresholds\n"
            info += "• Aggressive morphology\n"
        elif detection['method'] == 'Adaptive Contrast':
            info += "• Multi-scale CLAHE\n"
            info += "• Local contrast masking\n"
            info += "• Adaptive enhancement\n"
        elif detection['method'] == 'Multi-Scale':
            info += "• Multiple image scales\n"
            info += "• Scale-combined edges\n"
            info += "• Large structure emphasis\n"

        self.info_text.insert(tk.END, info)

    def crop_current(self):
        if not self.detections or self.original_image is None:
            return

        detection = self.detections[self.current_detection_index]
        x, y, w, h = detection['bbox']

        # Add some padding
        padding = 15
        x = max(0, x - padding)
        y = max(0, y - padding)
        w = min(self.original_image.shape[1] - x, w + 2 * padding)
        h = min(self.original_image.shape[0] - y, h + 2 * padding)

        # Crop the image
        cropped = self.original_image[y:y+h, x:x+w]

        # Save the cropped image
        save_path = filedialog.asksaveasfilename(
            title="Save cropped card",
            defaultextension=".jpg",
            filetypes=[
                ("JPEG files", "*.jpg"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )

        if save_path:
            cv2.imwrite(save_path, cropped)
            messagebox.showinfo("Success", f"Card cropped and saved to:\n{save_path}")

def main():
    root = tk.Tk()
    app = EnhancedCardDetector(root)
    root.mainloop()

if __name__ == "__main__":
    main()

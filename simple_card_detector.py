import cv2
import numpy as np
import os

def crop_cards(image_path="test images/test6.jpg", 
               expand_top=55, expand_bottom=25, expand_left=5, expand_right=5):
    """
    Simple function to detect and crop cards from an image.
    
    Args:
        image_path: Path to image (default: "test images/test4.jpg")
        expand_top: % to expand upward (default: 120)
        expand_bottom: % to expand downward (default: 40) 
        expand_left: % to expand left (default: 40)
        expand_right: % to expand right (default: 40)
    
    Returns:
        List of cropped card image paths
    """
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Could not load image: {image_path}")
        return []
    
    print(f"Processing: {image_path}")
    
    # Convert to grayscale and find edges
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 30, 100)
    
    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours for card-like shapes
    height, width = image.shape[:2]

    print(f"height: {height}")
    print(f"width: {width}")

    min_area = (width * height) * 0  # At least 1% of image
    max_area = (width * height) * 1   # At most 90% of image
    
    print(f"min-area: {min_area}")
    print(f"max-area: {max_area}")

    card_contours = []
    for contour in contours:
        area = cv2.contourArea(contour)
        # print(f"area-before: {area}")
        if min_area < area < max_area:
            print(f"area: {area}")
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h
            print(f"ratio: {aspect_ratio}")
            if 1.2 <= aspect_ratio <= 3.0:  # Card-like aspect ratio
                card_contours.append(contour)
    
    # Sort by area and take top 2
    card_contours.sort(key=cv2.contourArea, reverse=True)
    card_contours = card_contours[:2]
    
    if not card_contours:
        print("No cards detected")
        return []
    
    print(f"Found {len(card_contours)} cards")
    
    # Create output directory
    os.makedirs("cropped_cards", exist_ok=True)
    
    saved_paths = []
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    
    for i, contour in enumerate(card_contours):
        # Get corner points
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        if len(approx) == 4:
            corners = approx.reshape(4, 2)
        else:
            # Use bounding rectangle if not exactly 4 corners
            rect = cv2.minAreaRect(contour)
            corners = cv2.boxPoints(rect).astype(int)
        
        # Order corners: top-left, top-right, bottom-right, bottom-left
        corners = corners.astype(np.float32)
        x_sorted = corners[np.argsort(corners[:, 0]), :]
        left_most = x_sorted[:2, :]
        right_most = x_sorted[2:, :]
        left_most = left_most[np.argsort(left_most[:, 1]), :]
        right_most = right_most[np.argsort(right_most[:, 1]), :]
        tl, bl = left_most
        tr, br = right_most
        ordered_corners = np.array([tl, tr, br, bl], dtype=np.float32)
        
        # Calculate expansion
        current_width = max(np.linalg.norm(tr - tl), np.linalg.norm(br - bl))
        current_height = max(np.linalg.norm(bl - tl), np.linalg.norm(br - tr))
        
        expand_top_px = (expand_top / 100.0) * current_height
        expand_bottom_px = (expand_bottom / 100.0) * current_height
        expand_left_px = (expand_left / 100.0) * current_width
        expand_right_px = (expand_right / 100.0) * current_width
        
        # Expand corners
        new_tl = np.array([max(0, tl[0] - expand_left_px), max(0, tl[1] - expand_top_px)])
        new_tr = np.array([min(width-1, tr[0] + expand_right_px), max(0, tr[1] - expand_top_px)])
        new_br = np.array([min(width-1, br[0] + expand_right_px), min(height-1, br[1] + expand_bottom_px)])
        new_bl = np.array([max(0, bl[0] - expand_left_px), min(height-1, bl[1] + expand_bottom_px)])
        
        expanded_corners = np.array([new_tl, new_tr, new_br, new_bl], dtype=np.float32)
        
        # Calculate output dimensions
        width_a = np.sqrt(((expanded_corners[2][0] - expanded_corners[3][0]) ** 2) + 
                         ((expanded_corners[2][1] - expanded_corners[3][1]) ** 2))
        width_b = np.sqrt(((expanded_corners[1][0] - expanded_corners[0][0]) ** 2) + 
                         ((expanded_corners[1][1] - expanded_corners[0][1]) ** 2))
        max_width = max(int(width_a), int(width_b))
        
        height_a = np.sqrt(((expanded_corners[1][0] - expanded_corners[2][0]) ** 2) + 
                          ((expanded_corners[1][1] - expanded_corners[2][1]) ** 2))
        height_b = np.sqrt(((expanded_corners[0][0] - expanded_corners[3][0]) ** 2) + 
                          ((expanded_corners[0][1] - expanded_corners[3][1]) ** 2))
        max_height = max(int(height_a), int(height_b))
        
        # Perspective transform
        dst = np.array([[0, 0], [max_width-1, 0], [max_width-1, max_height-1], [0, max_height-1]], dtype=np.float32)
        matrix = cv2.getPerspectiveTransform(expanded_corners, dst)
        cropped_card = cv2.warpPerspective(image, matrix, (max_width, max_height))
        
        # Save
        output_path = f"cropped_cards/{base_name}_card_{i+1}.jpg"
        cv2.imwrite(output_path, cropped_card)
        saved_paths.append(output_path)
        
        print(f"Card {i+1}: {output_path} ({cropped_card.shape[1]}x{cropped_card.shape[0]})")
    
    return saved_paths


# Just run it directly
if __name__ == "__main__":
    card_paths = crop_cards()
    
    if card_paths:
        print(f"\n✅ Success! Cropped {len(card_paths)} cards:")
        for path in card_paths:
            print(f"  {path}")
    else:
        print("❌ No cards found")

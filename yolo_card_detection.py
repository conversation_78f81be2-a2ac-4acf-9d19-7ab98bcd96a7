"""
YOLOv8 Card Detection
Using YOLOv8 with a pre-trained model for document/object detection
"""

import os
import cv2
import numpy as np
from typing import List, <PERSON><PERSON>

def install_yolo():
    """Install YOLOv8"""
    print("📦 Installing YOLOv8...")
    os.system("pip install ultralytics")

def detect_cards_with_yolo(image_path="test images/test4.jpg", 
                          expand_top=100, expand_bottom=40, 
                          expand_left=40, expand_right=40,
                          debugging=True):
    """
    Detect cards using YOLOv8
    
    Args:
        image_path: Path to image
        expand_top/bottom/left/right: Expansion percentages
        debugging: Print debug info
    
    Returns:
        List of cropped card paths
    """
    
    if debugging:
        print(f"🔍 Processing with YOLOv8: {image_path}")
    
    try:
        from ultralytics import YOLO
        
        # Load YOLOv8 model (will download automatically on first use)
        # Using YOLOv8n (nano) for speed, can use YOLOv8s, YOLOv8m, YOLOv8l, YOLOv8x for better accuracy
        model = YOLO('yolov8n.pt')
        
        if debugging:
            print("🤖 YOLOv8 model loaded successfully")
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return []
        
        height, width = image.shape[:2]
        
        if debugging:
            print(f"📏 Image dimensions: {width}x{height}")
        
        # Run inference
        results = model(image_path, verbose=False)
        
        if not results or len(results) == 0:
            if debugging:
                print("❌ No objects detected")
            return []
        
        # Get detection results
        result = results[0]
        boxes = result.boxes
        
        if boxes is None or len(boxes) == 0:
            if debugging:
                print("❌ No bounding boxes found")
            return []
        
        if debugging:
            print(f"📦 Found {len(boxes)} objects")
        
        # Filter for relevant objects (books, laptops, cell phones, etc. that might be card-like)
        # COCO class IDs that might be relevant for cards/documents:
        # 73: book, 67: cell phone, 63: laptop, 84: book, etc.
        relevant_classes = [73, 67, 63, 84, 76]  # book, cell phone, laptop, book, keyboard
        
        card_candidates = []
        
        for i, box in enumerate(boxes):
            class_id = int(box.cls[0])
            confidence = float(box.conf[0])
            
            # Get bounding box coordinates
            x1, y1, x2, y2 = box.xyxy[0].tolist()
            x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
            
            # Calculate area and aspect ratio
            box_width = x2 - x1
            box_height = y2 - y1
            area = box_width * box_height
            aspect_ratio = box_width / box_height if box_height > 0 else 0
            
            if debugging:
                class_name = model.names[class_id] if class_id in model.names else "unknown"
                print(f"  Object {i+1}: {class_name} (class {class_id})")
                print(f"    Confidence: {confidence:.2f}")
                print(f"    Box: ({x1}, {y1}) to ({x2}, {y2})")
                print(f"    Size: {box_width}x{box_height}, Area: {area}")
                print(f"    Aspect ratio: {aspect_ratio:.2f}")
            
            # Filter by confidence, size, and aspect ratio
            min_area = (width * height) * 0.01  # At least 1% of image
            max_area = (width * height) * 0.8   # At most 80% of image
            
            if (confidence > 0.3 and  # Reasonable confidence
                min_area < area < max_area and  # Reasonable size
                0.3 < aspect_ratio < 4.0):  # Card-like aspect ratio
                
                card_candidates.append({
                    'box': (x1, y1, x2, y2),
                    'confidence': confidence,
                    'class_id': class_id,
                    'area': area
                })
                
                if debugging:
                    print(f"    ✅ Accepted as card candidate")
            else:
                if debugging:
                    print(f"    ❌ Rejected (conf={confidence:.2f}, area={area}, ratio={aspect_ratio:.2f})")
        
        # If no relevant classes found, take the largest objects
        if not card_candidates:
            if debugging:
                print("🔄 No relevant classes found, taking largest objects...")
            
            for i, box in enumerate(boxes):
                confidence = float(box.conf[0])
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                
                box_width = x2 - x1
                box_height = y2 - y1
                area = box_width * box_height
                aspect_ratio = box_width / box_height if box_height > 0 else 0
                
                min_area = (width * height) * 0.005  # Lower threshold
                max_area = (width * height) * 0.8
                
                if (confidence > 0.2 and
                    min_area < area < max_area and
                    0.2 < aspect_ratio < 5.0):
                    
                    card_candidates.append({
                        'box': (x1, y1, x2, y2),
                        'confidence': confidence,
                        'class_id': int(box.cls[0]),
                        'area': area
                    })
        
        if not card_candidates:
            if debugging:
                print("❌ No suitable card candidates found")
            return []
        
        # Sort by area (largest first) and take top 2
        card_candidates.sort(key=lambda x: x['area'], reverse=True)
        card_candidates = card_candidates[:2]
        
        if debugging:
            print(f"🎯 Selected {len(card_candidates)} card candidates")
        
        # Create output directory
        os.makedirs("yolo_cropped_cards", exist_ok=True)
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        saved_paths = []
        
        for i, candidate in enumerate(card_candidates):
            if debugging:
                print(f"\n🔧 Processing card {i+1}...")
            
            x1, y1, x2, y2 = candidate['box']
            
            # Apply expansion
            card_width = x2 - x1
            card_height = y2 - y1
            
            expand_top_px = int((expand_top / 100.0) * card_height)
            expand_bottom_px = int((expand_bottom / 100.0) * card_height)
            expand_left_px = int((expand_left / 100.0) * card_width)
            expand_right_px = int((expand_right / 100.0) * card_width)
            
            # Calculate expanded bounds
            crop_x1 = max(0, x1 - expand_left_px)
            crop_y1 = max(0, y1 - expand_top_px)
            crop_x2 = min(width, x2 + expand_right_px)
            crop_y2 = min(height, y2 + expand_bottom_px)
            
            if debugging:
                print(f"  Original bounds: ({x1}, {y1}) to ({x2}, {y2})")
                print(f"  Card size: {card_width}x{card_height}")
                print(f"  Expansion: top={expand_top_px}px, bottom={expand_bottom_px}px, left={expand_left_px}px, right={expand_right_px}px")
                print(f"  Final bounds: ({crop_x1}, {crop_y1}) to ({crop_x2}, {crop_y2})")
                print(f"  Confidence: {candidate['confidence']:.2f}")
            
            # Crop the card
            cropped_card = image[crop_y1:crop_y2, crop_x1:crop_x2]
            
            if cropped_card.size > 0:
                output_path = f"yolo_cropped_cards/{base_name}_yolo_card_{i+1}.jpg"
                cv2.imwrite(output_path, cropped_card)
                saved_paths.append(output_path)
                
                if debugging:
                    print(f"  ✅ Saved: {output_path} ({cropped_card.shape[1]}x{cropped_card.shape[0]})")
            else:
                if debugging:
                    print(f"  ❌ Empty crop result")
        
        return saved_paths
        
    except ImportError:
        print("❌ YOLOv8 not installed!")
        print("Installing YOLOv8...")
        install_yolo()
        print("✅ Please run the script again after installation")
        return []
    except Exception as e:
        print(f"❌ Error with YOLOv8: {str(e)}")
        return []

def test_yolo_detection():
    """Test YOLOv8 detection"""
    
    print("🎯 YOLOV8 CARD DETECTION TEST")
    print("=" * 50)
    
    card_paths = detect_cards_with_yolo("test images/test4.jpg", debugging=True)
    
    if card_paths:
        print(f"\n✅ SUCCESS! Detected {len(card_paths)} cards:")
        for i, path in enumerate(card_paths, 1):
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024
                print(f"  📄 Card {i}: {path} ({file_size:.1f} KB)")
        
        print(f"\n📁 Cards saved to: yolo_cropped_cards/")
        print("🔍 These should include complete cards with person photos!")
        
    else:
        print("❌ No cards detected")
    
    return card_paths

if __name__ == "__main__":
    test_yolo_detection()

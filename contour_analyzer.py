import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import math

class ContourAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Contour Analyzer")
        self.root.geometry("1200x800")
        
        # Variables
        self.image = None
        self.original_image = None
        self.contours = []
        self.current_contour_index = 0
        self.display_image = None
        self.edges = None
        
        # Create GUI
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control frame (top)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection
        ttk.Button(control_frame, text="Select Image", command=self.select_image).pack(side=tk.LEFT, padx=(0, 10))
        
        # Navigation buttons
        self.prev_btn = ttk.Button(control_frame, text="Previous", command=self.prev_contour, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.next_btn = ttk.Button(control_frame, text="Next", command=self.next_contour, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Contour counter
        self.counter_label = ttk.Label(control_frame, text="No contours detected")
        self.counter_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection parameters frame
        params_frame = ttk.LabelFrame(control_frame, text="Detection Parameters")
        params_frame.pack(side=tk.LEFT, padx=(10, 0), fill=tk.Y)

        # First row of parameters
        params_row1 = ttk.Frame(params_frame)
        params_row1.pack(padx=5, pady=2)

        ttk.Label(params_row1, text="Min Area:").pack(side=tk.LEFT, padx=(0, 2))
        self.min_area_var = tk.StringVar(value="100")
        ttk.Entry(params_row1, textvariable=self.min_area_var, width=6).pack(side=tk.LEFT, padx=(0, 8))

        ttk.Label(params_row1, text="Blur:").pack(side=tk.LEFT, padx=(0, 2))
        self.blur_var = tk.StringVar(value="5")
        ttk.Entry(params_row1, textvariable=self.blur_var, width=4).pack(side=tk.LEFT, padx=(0, 8))

        # Second row of parameters
        params_row2 = ttk.Frame(params_frame)
        params_row2.pack(padx=5, pady=2)

        ttk.Label(params_row2, text="Canny Low:").pack(side=tk.LEFT, padx=(0, 2))
        self.canny_low_var = tk.StringVar(value="50")
        ttk.Entry(params_row2, textvariable=self.canny_low_var, width=4).pack(side=tk.LEFT, padx=(0, 8))

        ttk.Label(params_row2, text="High:").pack(side=tk.LEFT, padx=(0, 2))
        self.canny_high_var = tk.StringVar(value="150")
        ttk.Entry(params_row2, textvariable=self.canny_high_var, width=4).pack(side=tk.LEFT, padx=(0, 8))

        # Third row - mode and redetect button
        params_row3 = ttk.Frame(params_frame)
        params_row3.pack(padx=5, pady=2)

        ttk.Label(params_row3, text="Mode:").pack(side=tk.LEFT, padx=(0, 2))
        self.contour_mode_var = tk.StringVar(value="EXTERNAL")
        mode_combo = ttk.Combobox(params_row3, textvariable=self.contour_mode_var, width=8, state="readonly")
        mode_combo['values'] = ("EXTERNAL", "LIST", "TREE")
        mode_combo.pack(side=tk.LEFT, padx=(0, 8))

        ttk.Button(params_row3, text="Redetect", command=self.detect_contours).pack(side=tk.LEFT, padx=(5, 0))

        # Fourth row - display options
        params_row4 = ttk.Frame(params_frame)
        params_row4.pack(padx=5, pady=2)

        self.show_edges_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(params_row4, text="Show Edges", variable=self.show_edges_var,
                       command=self.display_current_contour).pack(side=tk.LEFT, padx=(0, 5))

        self.show_all_contours_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_row4, text="Show All", variable=self.show_all_contours_var,
                       command=self.display_current_contour).pack(side=tk.LEFT)
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Image display frame (left)
        image_frame = ttk.LabelFrame(content_frame, text="Image Display")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Canvas for image display
        self.canvas = tk.Canvas(image_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Info frame (right)
        info_frame = ttk.LabelFrame(content_frame, text="Contour Information")
        info_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 0))
        info_frame.configure(width=300)
        info_frame.pack_propagate(False)
        
        # Info text widget
        self.info_text = tk.Text(info_frame, width=35, height=30, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
    def select_image(self):
        file_path = filedialog.askopenfilename(
            title="Select an image",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.original_image = cv2.imread(file_path)
                if self.original_image is None:
                    messagebox.showerror("Error", "Could not load the image file.")
                    return
                
                self.image = self.original_image.copy()
                self.detect_contours()
                self.display_current_contour()
                
            except Exception as e:
                messagebox.showerror("Error", f"Error loading image: {str(e)}")
    
    def detect_contours(self):
        if self.original_image is None:
            return

        try:
            min_area = float(self.min_area_var.get())
            blur_size = int(self.blur_var.get())
            canny_low = int(self.canny_low_var.get())
            canny_high = int(self.canny_high_var.get())
        except ValueError:
            min_area = 100
            blur_size = 5
            canny_low = 50
            canny_high = 150

        # Ensure blur size is odd and positive
        if blur_size % 2 == 0:
            blur_size += 1
        blur_size = max(1, blur_size)

        # Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (blur_size, blur_size), 0)

        # Edge detection
        self.edges = cv2.Canny(blurred, canny_low, canny_high)

        # Determine contour retrieval mode
        mode_map = {
            "EXTERNAL": cv2.RETR_EXTERNAL,
            "LIST": cv2.RETR_LIST,
            "TREE": cv2.RETR_TREE
        }
        retrieval_mode = mode_map.get(self.contour_mode_var.get(), cv2.RETR_EXTERNAL)

        # Find contours
        contours, _ = cv2.findContours(self.edges, retrieval_mode, cv2.CHAIN_APPROX_SIMPLE)

        # Filter contours by area and sort by area (largest first)
        self.contours = [c for c in contours if cv2.contourArea(c) >= min_area]
        self.contours.sort(key=cv2.contourArea, reverse=True)
        
        self.current_contour_index = 0
        
        # Update UI
        if self.contours:
            self.prev_btn.config(state=tk.NORMAL)
            self.next_btn.config(state=tk.NORMAL)
            self.update_counter_label()
            self.display_current_contour()
        else:
            self.prev_btn.config(state=tk.DISABLED)
            self.next_btn.config(state=tk.DISABLED)
            self.counter_label.config(text="No contours detected")
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, "No contours found with the current parameters.")
    
    def prev_contour(self):
        if self.contours and self.current_contour_index > 0:
            self.current_contour_index -= 1
            self.update_counter_label()
            self.display_current_contour()
    
    def next_contour(self):
        if self.contours and self.current_contour_index < len(self.contours) - 1:
            self.current_contour_index += 1
            self.update_counter_label()
            self.display_current_contour()
    
    def update_counter_label(self):
        if self.contours:
            self.counter_label.config(
                text=f"Contour {self.current_contour_index + 1} of {len(self.contours)}"
            )
    
    def display_current_contour(self):
        if self.original_image is None:
            return

        # Check if we should show edges
        if self.show_edges_var.get() and self.edges is not None:
            # Convert edges to 3-channel for display
            display_img = cv2.cvtColor(self.edges, cv2.COLOR_GRAY2BGR)
        else:
            # Create a copy of the original image
            display_img = self.original_image.copy()

        if not self.contours:
            self.display_image_on_canvas(display_img)
            return

        # Draw all contours in light gray if option is enabled
        if self.show_all_contours_var.get():
            cv2.drawContours(display_img, self.contours, -1, (128, 128, 128), 1)

        # Highlight current contour in red
        current_contour = self.contours[self.current_contour_index]
        cv2.drawContours(display_img, [current_contour], -1, (0, 0, 255), 3)

        # Draw bounding rectangle
        x, y, w, h = cv2.boundingRect(current_contour)
        cv2.rectangle(display_img, (x, y), (x + w, y + h), (255, 0, 0), 2)

        # Display the image
        self.display_image_on_canvas(display_img)

        # Update info panel
        self.update_info_panel(current_contour)
    
    def display_image_on_canvas(self, img):
        # Convert BGR to RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Get canvas dimensions
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, lambda: self.display_image_on_canvas(img))
            return
        
        # Calculate scaling factor to fit image in canvas
        img_height, img_width = img_rgb.shape[:2]
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # Don't upscale
        
        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        img_resized = cv2.resize(img_rgb, (new_width, new_height))
        
        # Convert to PIL Image and then to PhotoImage
        pil_img = Image.fromarray(img_resized)
        self.display_image = ImageTk.PhotoImage(pil_img)
        
        # Clear canvas and display image
        self.canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        self.canvas.create_image(x, y, anchor=tk.NW, image=self.display_image)
    
    def update_info_panel(self, contour):
        # Calculate contour properties
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        
        # Bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        
        # Minimum enclosing circle
        (cx, cy), radius = cv2.minEnclosingCircle(contour)
        
        # Aspect ratio
        aspect_ratio = float(w) / h if h != 0 else 0
        
        # Extent (ratio of contour area to bounding rectangle area)
        rect_area = w * h
        extent = float(area) / rect_area if rect_area != 0 else 0
        
        # Solidity (ratio of contour area to convex hull area)
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        solidity = float(area) / hull_area if hull_area != 0 else 0
        
        # Equivalent diameter
        equiv_diameter = np.sqrt(4 * area / np.pi)
        
        # Moments
        moments = cv2.moments(contour)
        
        # Centroid
        if moments['m00'] != 0:
            centroid_x = int(moments['m10'] / moments['m00'])
            centroid_y = int(moments['m01'] / moments['m00'])
        else:
            centroid_x = centroid_y = 0
        
        # Update info text
        info = f"""CONTOUR #{self.current_contour_index + 1}
Rank: {self.current_contour_index + 1} (by area)

BASIC PROPERTIES:
Area: {area:.2f} pixels
Perimeter: {perimeter:.2f} pixels
Number of points: {len(contour)}

BOUNDING RECTANGLE:
X: {x}, Y: {y}
Width: {w}, Height: {h}
Aspect Ratio: {aspect_ratio:.3f}

CENTROID:
X: {centroid_x}, Y: {centroid_y}

ENCLOSING CIRCLE:
Center: ({cx:.1f}, {cy:.1f})
Radius: {radius:.2f}

SHAPE ANALYSIS:
Extent: {extent:.3f}
Solidity: {solidity:.3f}
Equivalent Diameter: {equiv_diameter:.2f}

MOMENTS:
m00: {moments['m00']:.2f}
m10: {moments['m10']:.2f}
m01: {moments['m01']:.2f}
m20: {moments['m20']:.2f}
m11: {moments['m11']:.2f}
m02: {moments['m02']:.2f}

APPROXIMATION:
"""
        
        # Polygon approximation
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        info += f"Approx points (2%): {len(approx)}\n"
        
        epsilon = 0.01 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        info += f"Approx points (1%): {len(approx)}\n"
        
        # Clear and update info text
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(tk.END, info)

def main():
    root = tk.Tk()
    app = ContourAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()

import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import math

class AdvancedCardDetector:
    def __init__(self, root):
        self.root = root
        self.root.title("Advanced Card Detector")
        self.root.geometry("1400x900")
        
        # Variables
        self.image = None
        self.original_image = None
        self.detections = []
        self.current_detection_index = 0
        self.display_image = None
        self.processed_image = None
        
        # Create GUI
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control frame (top)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection
        ttk.Button(control_frame, text="Select Image", command=self.select_image).pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection method selection
        ttk.Label(control_frame, text="Method:").pack(side=tk.LEFT, padx=(10, 5))
        self.method_var = tk.StringVar(value="Advanced OpenCV")
        method_combo = ttk.Combobox(control_frame, textvariable=self.method_var, width=15, state="readonly")
        method_combo['values'] = ("Advanced OpenCV", "Document Detection", "Morphological", "Adaptive")
        method_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection button
        ttk.Button(control_frame, text="Detect Cards", command=self.detect_cards).pack(side=tk.LEFT, padx=(5, 10))
        
        # Navigation buttons
        self.prev_btn = ttk.Button(control_frame, text="Previous", command=self.prev_detection, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.next_btn = ttk.Button(control_frame, text="Next", command=self.next_detection, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection counter
        self.counter_label = ttk.Label(control_frame, text="No detections")
        self.counter_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Crop button
        self.crop_btn = ttk.Button(control_frame, text="Crop Current", command=self.crop_current, state=tk.DISABLED)
        self.crop_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Parameters frame
        params_frame = ttk.LabelFrame(control_frame, text="Parameters")
        params_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Parameters row 1
        params_row1 = ttk.Frame(params_frame)
        params_row1.pack(padx=5, pady=2)
        
        ttk.Label(params_row1, text="Min Area:").pack(side=tk.LEFT, padx=(0, 2))
        self.min_area_var = tk.StringVar(value="5000")
        ttk.Entry(params_row1, textvariable=self.min_area_var, width=8).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row1, text="Aspect Ratio:").pack(side=tk.LEFT, padx=(5, 2))
        self.aspect_ratio_var = tk.StringVar(value="0.5")
        ttk.Entry(params_row1, textvariable=self.aspect_ratio_var, width=6).pack(side=tk.LEFT)
        
        # Parameters row 2
        params_row2 = ttk.Frame(params_frame)
        params_row2.pack(padx=5, pady=2)
        
        self.show_preprocessing_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(params_row2, text="Show Preprocessing", 
                       variable=self.show_preprocessing_var,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 5))
        
        self.show_all_detections_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_row2, text="Show All", 
                       variable=self.show_all_detections_var,
                       command=self.update_display).pack(side=tk.LEFT)
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Image display frame (left)
        image_frame = ttk.LabelFrame(content_frame, text="Image Display")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Canvas for image display
        self.canvas = tk.Canvas(image_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Info frame (right)
        info_frame = ttk.LabelFrame(content_frame, text="Detection Information")
        info_frame.pack(side=tk.RIGHT, fill=tk.Y)
        info_frame.configure(width=350)
        info_frame.pack_propagate(False)
        
        # Info text widget
        self.info_text = tk.Text(info_frame, width=40, height=35, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
    def select_image(self):
        file_path = filedialog.askopenfilename(
            title="Select an image",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.original_image = cv2.imread(file_path)
                if self.original_image is None:
                    messagebox.showerror("Error", "Could not load the image file.")
                    return
                
                self.image = self.original_image.copy()
                self.detections = []
                self.current_detection_index = 0
                self.update_ui_state()
                self.display_image_on_canvas(self.original_image)
                
            except Exception as e:
                messagebox.showerror("Error", f"Error loading image: {str(e)}")
    
    def detect_cards(self):
        if self.original_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return
        
        method = self.method_var.get()
        
        if method == "Advanced OpenCV":
            self.detect_with_advanced_opencv()
        elif method == "Document Detection":
            self.detect_with_document_detection()
        elif method == "Morphological":
            self.detect_with_morphological()
        elif method == "Adaptive":
            self.detect_with_adaptive()
        
        self.current_detection_index = 0
        self.update_ui_state()
        self.update_display()
    
    def detect_with_advanced_opencv(self):
        """Advanced OpenCV approach with better preprocessing"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            min_area = 5000
            min_aspect = 0.5
        
        # Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # Bilateral filter to reduce noise while preserving edges
        filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        # Multiple edge detection approaches
        # 1. Canny with multiple thresholds
        edges1 = cv2.Canny(filtered, 30, 80)
        edges2 = cv2.Canny(filtered, 50, 150)
        edges3 = cv2.Canny(filtered, 100, 200)
        
        # Combine edges
        edges_combined = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))
        
        # Morphological operations to close gaps
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        edges_closed = cv2.morphologyEx(edges_combined, cv2.MORPH_CLOSE, kernel)
        
        # Dilate to connect nearby edges
        edges_dilated = cv2.dilate(edges_closed, kernel, iterations=1)
        
        self.processed_image = edges_dilated
        
        # Find contours
        contours, _ = cv2.findContours(edges_dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)
            
            if aspect_ratio < min_aspect:
                continue
            
            # Approximate contour to polygon
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'approx_points': len(approx),
                'method': 'Advanced OpenCV'
            }
            self.detections.append(detection)
        
        # Sort by area (largest first)
        self.detections.sort(key=lambda x: x['area'], reverse=True)
    
    def detect_with_document_detection(self):
        """Document-style detection using adaptive thresholding"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            min_area = 5000
            min_aspect = 0.5
        
        # Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
        
        # Adaptive thresholding
        adaptive_thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Invert if needed (make sure objects are white)
        if np.mean(adaptive_thresh) > 127:
            adaptive_thresh = cv2.bitwise_not(adaptive_thresh)
        
        # Morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        cleaned = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
        
        self.processed_image = cleaned
        
        # Find contours
        contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue
            
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)
            
            if aspect_ratio < min_aspect:
                continue
            
            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'method': 'Document Detection'
            }
            self.detections.append(detection)
        
        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def detect_with_morphological(self):
        """Morphological approach for card detection"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            min_area = 5000
            min_aspect = 0.5

        # Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Otsu's thresholding
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (10, 10))

        # Close small gaps
        closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # Remove small noise
        opened = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel)

        self.processed_image = opened

        # Find contours
        contours, _ = cv2.findContours(opened, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'method': 'Morphological'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def detect_with_adaptive(self):
        """Adaptive approach combining multiple techniques"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
        except ValueError:
            min_area = 5000
            min_aspect = 0.5

        # Convert to grayscale
        gray = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)

        # Try multiple preprocessing approaches and combine results

        # Approach 1: Edge-based
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        edges = cv2.Canny(blurred, 50, 150)
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        contours1, _ = cv2.findContours(edges_closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Approach 2: Threshold-based
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        contours2, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Combine and deduplicate contours
        all_contours = list(contours1) + list(contours2)

        self.processed_image = edges_closed  # Show edge detection result

        self.detections = []
        processed_areas = set()

        for contour in all_contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            # Simple deduplication based on center point
            center = (x + w//2, y + h//2)
            area_key = (center[0]//50, center[1]//50, int(area//1000))  # Grid-based dedup

            if area_key in processed_areas:
                continue
            processed_areas.add(area_key)

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'method': 'Adaptive'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: x['area'], reverse=True)

    def prev_detection(self):
        if self.detections and self.current_detection_index > 0:
            self.current_detection_index -= 1
            self.update_counter_label()
            self.update_display()

    def next_detection(self):
        if self.detections and self.current_detection_index < len(self.detections) - 1:
            self.current_detection_index += 1
            self.update_counter_label()
            self.update_display()

    def update_ui_state(self):
        if self.detections:
            self.prev_btn.config(state=tk.NORMAL)
            self.next_btn.config(state=tk.NORMAL)
            self.crop_btn.config(state=tk.NORMAL)
            self.update_counter_label()
        else:
            self.prev_btn.config(state=tk.DISABLED)
            self.next_btn.config(state=tk.DISABLED)
            self.crop_btn.config(state=tk.DISABLED)
            self.counter_label.config(text="No detections")

    def update_counter_label(self):
        if self.detections:
            self.counter_label.config(
                text=f"Detection {self.current_detection_index + 1} of {len(self.detections)}"
            )

    def update_display(self):
        if self.original_image is None:
            return

        # Choose what to display
        if self.show_preprocessing_var.get() and self.processed_image is not None:
            # Show preprocessing result
            if len(self.processed_image.shape) == 2:  # Grayscale
                display_img = cv2.cvtColor(self.processed_image, cv2.COLOR_GRAY2BGR)
            else:
                display_img = self.processed_image.copy()
        else:
            # Show original image
            display_img = self.original_image.copy()

        if not self.detections:
            self.display_image_on_canvas(display_img)
            self.update_info_panel(None)
            return

        # Draw all detections if enabled
        if self.show_all_detections_var.get():
            for i, detection in enumerate(self.detections):
                color = (128, 128, 128) if i != self.current_detection_index else (0, 0, 255)
                thickness = 1 if i != self.current_detection_index else 3
                cv2.drawContours(display_img, [detection['contour']], -1, color, thickness)

        # Highlight current detection
        if self.detections:
            current_detection = self.detections[self.current_detection_index]
            cv2.drawContours(display_img, [current_detection['contour']], -1, (0, 0, 255), 3)

            # Draw bounding rectangle
            x, y, w, h = current_detection['bbox']
            cv2.rectangle(display_img, (x, y), (x + w, y + h), (255, 0, 0), 2)

            # Draw detection number
            cv2.putText(display_img, f"#{self.current_detection_index + 1}",
                       (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        self.display_image_on_canvas(display_img)
        self.update_info_panel(current_detection if self.detections else None)

    def display_image_on_canvas(self, img):
        # Convert BGR to RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Get canvas dimensions
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, lambda: self.display_image_on_canvas(img))
            return

        # Calculate scaling factor to fit image in canvas
        img_height, img_width = img_rgb.shape[:2]
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # Don't upscale

        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        img_resized = cv2.resize(img_rgb, (new_width, new_height))

        # Convert to PIL Image and then to PhotoImage
        pil_img = Image.fromarray(img_resized)
        self.display_image = ImageTk.PhotoImage(pil_img)

        # Clear canvas and display image
        self.canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        self.canvas.create_image(x, y, anchor=tk.NW, image=self.display_image)

    def update_info_panel(self, detection):
        self.info_text.delete(1.0, tk.END)

        if detection is None:
            self.info_text.insert(tk.END, "No detection selected or no detections found.")
            return

        # Calculate additional properties
        contour = detection['contour']
        area = detection['area']
        x, y, w, h = detection['bbox']

        # Perimeter
        perimeter = cv2.arcLength(contour, True)

        # Minimum enclosing circle
        (cx, cy), radius = cv2.minEnclosingCircle(contour)

        # Extent (ratio of contour area to bounding rectangle area)
        rect_area = w * h
        extent = float(area) / rect_area if rect_area != 0 else 0

        # Solidity (ratio of contour area to convex hull area)
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        solidity = float(area) / hull_area if hull_area != 0 else 0

        # Moments
        moments = cv2.moments(contour)

        # Centroid
        if moments['m00'] != 0:
            centroid_x = int(moments['m10'] / moments['m00'])
            centroid_y = int(moments['m01'] / moments['m00'])
        else:
            centroid_x = centroid_y = 0

        info = f"""DETECTION #{self.current_detection_index + 1}
Method: {detection['method']}
Rank: {self.current_detection_index + 1} (by area)

BASIC PROPERTIES:
Area: {area:.2f} pixels
Perimeter: {perimeter:.2f} pixels
Number of points: {len(contour)}

BOUNDING RECTANGLE:
X: {x}, Y: {y}
Width: {w}, Height: {h}
Aspect Ratio: {detection['aspect_ratio']:.3f}

CENTROID:
X: {centroid_x}, Y: {centroid_y}

ENCLOSING CIRCLE:
Center: ({cx:.1f}, {cy:.1f})
Radius: {radius:.2f}

SHAPE ANALYSIS:
Extent: {extent:.3f}
Solidity: {solidity:.3f}

DETECTION QUALITY:
Rectangularity: {extent:.3f}
Convexity: {solidity:.3f}
"""

        if 'approx_points' in detection:
            info += f"Polygon Approx: {detection['approx_points']} points\n"

        # Add method-specific information
        if detection['method'] == 'Advanced OpenCV':
            info += "\nADVANCED OPENCV METHOD:\n"
            info += "- CLAHE enhancement\n"
            info += "- Bilateral filtering\n"
            info += "- Multi-threshold Canny\n"
            info += "- Morphological closing\n"
        elif detection['method'] == 'Document Detection':
            info += "\nDOCUMENT DETECTION METHOD:\n"
            info += "- Adaptive thresholding\n"
            info += "- Morphological operations\n"
            info += "- Optimized for documents\n"
        elif detection['method'] == 'Morphological':
            info += "\nMORPHOLOGICAL METHOD:\n"
            info += "- Otsu thresholding\n"
            info += "- Large kernel operations\n"
            info += "- Good for high contrast\n"
        elif detection['method'] == 'Adaptive':
            info += "\nADAPTIVE METHOD:\n"
            info += "- Multiple approaches combined\n"
            info += "- Edge + threshold detection\n"
            info += "- Deduplication applied\n"

        self.info_text.insert(tk.END, info)

    def crop_current(self):
        if not self.detections or self.original_image is None:
            return

        detection = self.detections[self.current_detection_index]
        x, y, w, h = detection['bbox']

        # Add some padding
        padding = 10
        x = max(0, x - padding)
        y = max(0, y - padding)
        w = min(self.original_image.shape[1] - x, w + 2 * padding)
        h = min(self.original_image.shape[0] - y, h + 2 * padding)

        # Crop the image
        cropped = self.original_image[y:y+h, x:x+w]

        # Save the cropped image
        save_path = filedialog.asksaveasfilename(
            title="Save cropped image",
            defaultextension=".jpg",
            filetypes=[
                ("JPEG files", "*.jpg"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )

        if save_path:
            cv2.imwrite(save_path, cropped)
            messagebox.showinfo("Success", f"Cropped image saved to: {save_path}")

def main():
    root = tk.Tk()
    app = AdvancedCardDetector(root)
    root.mainloop()

if __name__ == "__main__":
    main()

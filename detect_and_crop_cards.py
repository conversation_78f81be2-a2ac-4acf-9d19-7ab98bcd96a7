import cv2
import numpy as np
import os
from typing import List, Tuple, Optional
import argparse


def preprocess_image(image: np.ndarray) -> np.ndarray:
    """
    Preprocess the image for better edge detection.
    """
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Apply adaptive threshold for better edge detection
    thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY, 11, 2)

    # Apply morphological operations to clean up the image
    kernel = np.ones((3, 3), np.uint8)
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

    return cleaned


def find_card_contours(image: np.ndarray, preprocessed: np.ndarray) -> List[np.ndarray]:
    """
    Find contours that likely represent cards in the image.
    """
    # Find edges using Canny edge detection
    edges = cv2.Canny(preprocessed, 50, 150, apertureSize=3)

    # Dilate edges to connect broken lines
    kernel = np.ones((3, 3), np.uint8)
    edges = cv2.dilate(edges, kernel, iterations=1)

    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours based on area and aspect ratio
    height, width = image.shape[:2]
    min_area = (width * height) * 0.02  # Minimum 2% of image area (reduced from 5%)
    max_area = (width * height) * 0.8   # Maximum 80% of image area

    card_contours = []

    for contour in contours:
        area = cv2.contourArea(contour)

        if min_area < area < max_area:
            # Approximate the contour to a polygon
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            # Check if it's roughly rectangular (4 corners) or can be simplified to rectangle
            if len(approx) >= 4:
                # Get bounding rectangle to check aspect ratio
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = float(w) / h

                # Cards typically have aspect ratio between 0.4 and 3.0 (expanded range)
                if 0.4 <= aspect_ratio <= 3.0:
                    card_contours.append(contour)

    # Sort by area (largest first) and return top candidates
    card_contours.sort(key=cv2.contourArea, reverse=True)
    return card_contours[:2]  # Return top 2 candidates


def get_card_corners(contour: np.ndarray) -> np.ndarray:
    """
    Get the four corners of a card from its contour.
    """
    # Approximate the contour to get corner points
    epsilon = 0.02 * cv2.arcLength(contour, True)
    approx = cv2.approxPolyDP(contour, epsilon, True)

    if len(approx) == 4:
        return approx.reshape(4, 2)

    # If we don't get exactly 4 points, use the bounding rectangle
    rect = cv2.minAreaRect(contour)
    box = cv2.boxPoints(rect)
    return np.int0(box)


def order_points(pts: np.ndarray) -> np.ndarray:
    """
    Order points in the order: top-left, top-right, bottom-right, bottom-left.
    """
    # Sort points based on their x-coordinates
    x_sorted = pts[np.argsort(pts[:, 0]), :]

    # Get left and right points
    left_most = x_sorted[:2, :]
    right_most = x_sorted[2:, :]

    # Sort left points by y-coordinate (top-left, bottom-left)
    left_most = left_most[np.argsort(left_most[:, 1]), :]
    tl, bl = left_most

    # Sort right points by y-coordinate (top-right, bottom-right)
    right_most = right_most[np.argsort(right_most[:, 1]), :]
    tr, br = right_most

    return np.array([tl, tr, br, bl], dtype=np.float32)


def expand_corners(corners: np.ndarray, image_shape: tuple,
                  expand_top: float, expand_bottom: float,
                  expand_left: float, expand_right: float) -> np.ndarray:
    """
    Expand the detected corners by the specified percentages.

    Args:
        corners: Original corner points [top-left, top-right, bottom-right, bottom-left]
        image_shape: Shape of the original image (height, width)
        expand_top, expand_bottom, expand_left, expand_right: Expansion percentages

    Returns:
        Expanded corner points
    """
    height, width = image_shape[:2]

    # Order the corners: top-left, top-right, bottom-right, bottom-left
    ordered_corners = order_points(corners)
    tl, tr, br, bl = ordered_corners

    # Calculate current dimensions
    current_width = max(np.linalg.norm(tr - tl), np.linalg.norm(br - bl))
    current_height = max(np.linalg.norm(bl - tl), np.linalg.norm(br - tr))

    # Calculate expansion amounts in pixels
    expand_top_px = (expand_top / 100.0) * current_height
    expand_bottom_px = (expand_bottom / 100.0) * current_height
    expand_left_px = (expand_left / 100.0) * current_width
    expand_right_px = (expand_right / 100.0) * current_width

    # Expand corners
    # Top-left: move up and left
    new_tl = np.array([
        max(0, tl[0] - expand_left_px),
        max(0, tl[1] - expand_top_px)
    ])

    # Top-right: move up and right
    new_tr = np.array([
        min(width - 1, tr[0] + expand_right_px),
        max(0, tr[1] - expand_top_px)
    ])

    # Bottom-right: move down and right
    new_br = np.array([
        min(width - 1, br[0] + expand_right_px),
        min(height - 1, br[1] + expand_bottom_px)
    ])

    # Bottom-left: move down and left
    new_bl = np.array([
        max(0, bl[0] - expand_left_px),
        min(height - 1, bl[1] + expand_bottom_px)
    ])

    return np.array([new_tl, new_tr, new_br, new_bl], dtype=np.float32)


def perspective_transform(image: np.ndarray, corners: np.ndarray) -> np.ndarray:
    """
    Apply perspective transformation to get a rectangular view of the card.
    """
    # Order the corners
    ordered_corners = order_points(corners)

    # Calculate the width and height of the new image
    width_a = np.sqrt(((ordered_corners[2][0] - ordered_corners[3][0]) ** 2) +
                     ((ordered_corners[2][1] - ordered_corners[3][1]) ** 2))
    width_b = np.sqrt(((ordered_corners[1][0] - ordered_corners[0][0]) ** 2) +
                     ((ordered_corners[1][1] - ordered_corners[0][1]) ** 2))
    max_width = max(int(width_a), int(width_b))

    height_a = np.sqrt(((ordered_corners[1][0] - ordered_corners[2][0]) ** 2) +
                      ((ordered_corners[1][1] - ordered_corners[2][1]) ** 2))
    height_b = np.sqrt(((ordered_corners[0][0] - ordered_corners[3][0]) ** 2) +
                      ((ordered_corners[0][1] - ordered_corners[3][1]) ** 2))
    max_height = max(int(height_a), int(height_b))

    # Define destination points for the perspective transformation
    dst = np.array([
        [0, 0],
        [max_width - 1, 0],
        [max_width - 1, max_height - 1],
        [0, max_height - 1]
    ], dtype=np.float32)

    # Calculate the perspective transformation matrix
    matrix = cv2.getPerspectiveTransform(ordered_corners, dst)

    # Apply the perspective transformation
    warped = cv2.warpPerspective(image, matrix, (max_width, max_height))

    return warped


def crop_cards_from_image(image_path: str, output_dir: str = "cropped_cards",
                         expand_top: float = 50.0, expand_bottom: float = 20.0,
                         expand_left: float = 20.0, expand_right: float = 20.0) -> List[str]:
    """
    Main function to detect and crop cards from an image.

    Args:
        image_path: Path to the input image
        output_dir: Directory to save cropped cards
        expand_top: Percentage to expand the detected area upward (default: 50%)
        expand_bottom: Percentage to expand the detected area downward (default: 20%)
        expand_left: Percentage to expand the detected area to the left (default: 20%)
        expand_right: Percentage to expand the detected area to the right (default: 20%)

    Returns:
        List of paths to the saved cropped card images
    """
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    print(f"Processing image: {image_path}")
    print(f"Image dimensions: {image.shape[1]}x{image.shape[0]}")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Preprocess the image
    preprocessed = preprocess_image(image)

    # Find card contours
    card_contours = find_card_contours(image, preprocessed)

    if len(card_contours) == 0:
        print("No cards detected in the image.")
        return []

    print(f"Found {len(card_contours)} potential cards")

    saved_paths = []
    base_name = os.path.splitext(os.path.basename(image_path))[0]

    for i, contour in enumerate(card_contours):
        try:
            # Get card corners
            corners = get_card_corners(contour)

            # Expand corners to include more of the card
            expanded_corners = expand_corners(corners, image.shape,
                                            expand_top, expand_bottom,
                                            expand_left, expand_right)

            # Apply perspective transformation with expanded corners
            cropped_card = perspective_transform(image, expanded_corners)

            # Save the cropped card
            output_path = os.path.join(output_dir, f"{base_name}_card_{i+1}.jpg")
            cv2.imwrite(output_path, cropped_card)
            saved_paths.append(output_path)

            print(f"Saved card {i+1} to: {output_path}")
            print(f"Card {i+1} dimensions: {cropped_card.shape[1]}x{cropped_card.shape[0]}")
            print(f"Expansion: top={expand_top}%, bottom={expand_bottom}%, left={expand_left}%, right={expand_right}%")

        except Exception as e:
            print(f"Error processing card {i+1}: {str(e)}")
            continue

    return saved_paths


def main():
    """
    Main function with command line interface.
    """
    parser = argparse.ArgumentParser(description="Automatically detect and crop cards from images")
    parser.add_argument("image_path", help="Path to the input image")
    parser.add_argument("--output-dir", default="cropped_cards",
                       help="Output directory for cropped cards (default: cropped_cards)")
    parser.add_argument("--expand-top", type=float, default=50.0,
                       help="Percentage to expand upward (default: 50)")
    parser.add_argument("--expand-bottom", type=float, default=20.0,
                       help="Percentage to expand downward (default: 20)")
    parser.add_argument("--expand-left", type=float, default=20.0,
                       help="Percentage to expand to the left (default: 20)")
    parser.add_argument("--expand-right", type=float, default=20.0,
                       help="Percentage to expand to the right (default: 20)")

    args = parser.parse_args()

    try:
        saved_paths = crop_cards_from_image(args.image_path, args.output_dir,
                                          args.expand_top, args.expand_bottom,
                                          args.expand_left, args.expand_right)

        if saved_paths:
            print(f"\nSuccessfully cropped {len(saved_paths)} cards:")
            for path in saved_paths:
                print(f"  - {path}")
        else:
            print("No cards were successfully cropped.")

    except Exception as e:
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    main()
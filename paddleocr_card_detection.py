"""
PaddleOCR Card Detection
Reliable card detection using PaddleOCR's document detection capabilities
"""

import os
import cv2
import numpy as np
from typing import List, Tuple

def install_paddleocr():
    """Install PaddleOCR"""
    print("📦 Installing PaddleOCR...")
    os.system("pip install paddlepaddle paddleocr")

def detect_cards_with_paddle(image_path="test images/test4.jpg", 
                           expand_top=80, expand_bottom=30, 
                           expand_left=30, expand_right=30,
                           debugging=True):
    """
    Detect cards using PaddleOCR's document detection
    
    Args:
        image_path: Path to image
        expand_top/bottom/left/right: Expansion percentages
        debugging: Print debug info
    
    Returns:
        List of cropped card paths
    """
    
    if debugging:
        print(f"🔍 Processing with PaddleOCR: {image_path}")
    
    try:
        from paddleocr import PaddleOCR
        
        # Initialize PaddleOCR
        # use_angle_cls=True enables text direction detection
        # use_gpu=False for CPU usage (set to True if you have GPU)
        ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False, show_log=False)
        
        if debugging:
            print("🤖 PaddleOCR initialized successfully")
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return []
        
        height, width = image.shape[:2]
        
        if debugging:
            print(f"📏 Image dimensions: {width}x{height}")
        
        # Run OCR detection
        result = ocr.ocr(image_path, cls=True)
        
        if not result or not result[0]:
            if debugging:
                print("❌ No text regions detected")
            return []
        
        if debugging:
            print(f"📝 Found {len(result[0])} text regions")
        
        # Extract bounding boxes from all detected text regions
        all_boxes = []
        for line in result[0]:
            box = line[0]  # Bounding box coordinates
            text = line[1][0]  # Detected text
            confidence = line[1][1]  # Confidence score
            
            if debugging:
                print(f"  Text: '{text}' (confidence: {confidence:.2f})")
                print(f"  Box: {box}")
            
            # Convert box coordinates to integers
            box_int = [[int(point[0]), int(point[1])] for point in box]
            all_boxes.append(box_int)
        
        if not all_boxes:
            if debugging:
                print("❌ No valid bounding boxes found")
            return []
        
        # Group nearby boxes to form card regions
        card_regions = group_boxes_into_cards(all_boxes, width, height, debugging)
        
        if not card_regions:
            if debugging:
                print("❌ Could not group boxes into card regions")
            return []
        
        # Create output directory
        os.makedirs("paddle_cropped_cards", exist_ok=True)
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        saved_paths = []
        
        for i, region in enumerate(card_regions):
            if debugging:
                print(f"\n🔧 Processing card region {i+1}...")
            
            # Get bounding rectangle of the region
            x_coords = [point[0] for box in region for point in box]
            y_coords = [point[1] for box in region for point in box]
            
            min_x, max_x = min(x_coords), max(x_coords)
            min_y, max_y = min(y_coords), max(y_coords)
            
            # Apply expansion
            card_width = max_x - min_x
            card_height = max_y - min_y
            
            expand_top_px = int((expand_top / 100.0) * card_height)
            expand_bottom_px = int((expand_bottom / 100.0) * card_height)
            expand_left_px = int((expand_left / 100.0) * card_width)
            expand_right_px = int((expand_right / 100.0) * card_width)
            
            # Calculate expanded bounds
            crop_x1 = max(0, min_x - expand_left_px)
            crop_y1 = max(0, min_y - expand_top_px)
            crop_x2 = min(width, max_x + expand_right_px)
            crop_y2 = min(height, max_y + expand_bottom_px)
            
            if debugging:
                print(f"  Original bounds: ({min_x}, {min_y}) to ({max_x}, {max_y})")
                print(f"  Card size: {card_width}x{card_height}")
                print(f"  Expansion: top={expand_top_px}px, bottom={expand_bottom_px}px, left={expand_left_px}px, right={expand_right_px}px")
                print(f"  Final bounds: ({crop_x1}, {crop_y1}) to ({crop_x2}, {crop_y2})")
            
            # Crop the card
            cropped_card = image[crop_y1:crop_y2, crop_x1:crop_x2]
            
            if cropped_card.size > 0:
                output_path = f"paddle_cropped_cards/{base_name}_paddle_card_{i+1}.jpg"
                cv2.imwrite(output_path, cropped_card)
                saved_paths.append(output_path)
                
                if debugging:
                    print(f"  ✅ Saved: {output_path} ({cropped_card.shape[1]}x{cropped_card.shape[0]})")
            else:
                if debugging:
                    print(f"  ❌ Empty crop result")
        
        return saved_paths
        
    except ImportError:
        print("❌ PaddleOCR not installed!")
        print("Installing PaddleOCR...")
        install_paddleocr()
        print("✅ Please run the script again after installation")
        return []
    except Exception as e:
        print(f"❌ Error with PaddleOCR: {str(e)}")
        return []

def group_boxes_into_cards(boxes, image_width, image_height, debugging=True):
    """
    Group nearby text boxes into card regions
    """
    
    if debugging:
        print(f"🔗 Grouping {len(boxes)} boxes into card regions...")
    
    # Calculate center points of each box
    centers = []
    for box in boxes:
        x_coords = [point[0] for point in box]
        y_coords = [point[1] for point in box]
        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)
        centers.append((center_x, center_y))
    
    # Group boxes by proximity
    card_regions = []
    used_boxes = set()
    
    # Distance threshold for grouping (adjust based on image size)
    distance_threshold = min(image_width, image_height) * 0.3
    
    if debugging:
        print(f"  Distance threshold: {distance_threshold:.0f} pixels")
    
    for i, (center_x, center_y) in enumerate(centers):
        if i in used_boxes:
            continue
        
        # Start a new card region
        current_region = [boxes[i]]
        used_boxes.add(i)
        
        # Find nearby boxes
        for j, (other_x, other_y) in enumerate(centers):
            if j in used_boxes:
                continue
            
            distance = np.sqrt((center_x - other_x)**2 + (center_y - other_y)**2)
            
            if distance < distance_threshold:
                current_region.append(boxes[j])
                used_boxes.add(j)
        
        # Only keep regions with multiple text boxes (likely cards)
        if len(current_region) >= 3:  # Cards typically have multiple text elements
            card_regions.append(current_region)
            if debugging:
                print(f"  Card region {len(card_regions)}: {len(current_region)} text boxes")
    
    # Sort regions by size (largest first)
    card_regions.sort(key=lambda region: len(region), reverse=True)
    
    # Take top 2 regions
    card_regions = card_regions[:2]
    
    if debugging:
        print(f"  Final card regions: {len(card_regions)}")
    
    return card_regions

def test_paddle_detection():
    """Test PaddleOCR detection"""
    
    print("🥞 PADDLEOCR CARD DETECTION TEST")
    print("=" * 50)
    
    card_paths = detect_cards_with_paddle("test images/test4.jpg", debugging=True)
    
    if card_paths:
        print(f"\n✅ SUCCESS! Detected {len(card_paths)} cards:")
        for i, path in enumerate(card_paths, 1):
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024
                print(f"  📄 Card {i}: {path} ({file_size:.1f} KB)")
        
        print(f"\n📁 Cards saved to: paddle_cropped_cards/")
        print("🔍 These should include complete cards with person photos!")
        
    else:
        print("❌ No cards detected")
    
    return card_paths

if __name__ == "__main__":
    test_paddle_detection()

"""
Reliable Card Detector using EasyOCR
The most reliable solution for detecting and cropping cards from images
"""

import os
import cv2
import numpy as np

def crop_cards(image_path="test images/test4.jpg", 
               expand_top=100, expand_bottom=40, expand_left=40, expand_right=40,
               debugging=False):
    """
    Reliably detect and crop cards using EasyOCR text detection.
    
    Args:
        image_path: Path to image (default: "test images/test4.jpg")
        expand_top: % to expand upward (default: 100)
        expand_bottom: % to expand downward (default: 40) 
        expand_left: % to expand left (default: 40)
        expand_right: % to expand right (default: 40)
        debugging: Print debug info (default: False)
    
    Returns:
        List of cropped card image paths
    """
    
    try:
        import easyocr
    except ImportError:
        print("❌ EasyOCR not installed! Installing...")
        os.system("pip install easyocr")
        print("✅ Please run the script again after installation")
        return []
    
    if debugging:
        print(f"🔍 Processing: {image_path}")
    
    # Initialize EasyOCR
    reader = easyocr.Reader(['en'], gpu=False, verbose=False)
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Could not load image: {image_path}")
        return []
    
    height, width = image.shape[:2]
    
    if debugging:
        print(f"📏 Image dimensions: {width}x{height}")
    
    # Run OCR detection
    results = reader.readtext(image_path)
    
    if not results:
        if debugging:
            print("❌ No text regions detected")
        return []
    
    if debugging:
        print(f"📝 Found {len(results)} text regions")
    
    # Extract bounding boxes
    all_boxes = []
    for result in results:
        bbox = result[0]  # Bounding box coordinates
        box_int = [[int(point[0]), int(point[1])] for point in bbox]
        all_boxes.append(box_int)
    
    # Group boxes into card regions
    card_regions = group_boxes_into_cards(all_boxes, width, height, debugging)
    
    if not card_regions:
        if debugging:
            print("❌ Could not group boxes into card regions")
        return []
    
    # Create output directory
    os.makedirs("reliable_cropped_cards", exist_ok=True)
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    
    saved_paths = []
    
    for i, region in enumerate(card_regions):
        if debugging:
            print(f"\n🔧 Processing card {i+1}...")
        
        # Get bounding rectangle of the region
        x_coords = [point[0] for box in region for point in box]
        y_coords = [point[1] for box in region for point in box]
        
        min_x, max_x = min(x_coords), max(x_coords)
        min_y, max_y = min(y_coords), max(y_coords)
        
        # Apply expansion
        card_width = max_x - min_x
        card_height = max_y - min_y
        
        expand_top_px = int((expand_top / 100.0) * card_height)
        expand_bottom_px = int((expand_bottom / 100.0) * card_height)
        expand_left_px = int((expand_left / 100.0) * card_width)
        expand_right_px = int((expand_right / 100.0) * card_width)
        
        # Calculate expanded bounds
        crop_x1 = max(0, min_x - expand_left_px)
        crop_y1 = max(0, min_y - expand_top_px)
        crop_x2 = min(width, max_x + expand_right_px)
        crop_y2 = min(height, max_y + expand_bottom_px)
        
        if debugging:
            print(f"  Card size: {card_width}x{card_height}")
            print(f"  Expansion: top={expand_top_px}px, bottom={expand_bottom_px}px, left={expand_left_px}px, right={expand_right_px}px")
            print(f"  Final bounds: ({crop_x1}, {crop_y1}) to ({crop_x2}, {crop_y2})")
        
        # Crop the card
        cropped_card = image[crop_y1:crop_y2, crop_x1:crop_x2]
        
        if cropped_card.size > 0:
            output_path = f"reliable_cropped_cards/{base_name}_card_{i+1}.jpg"
            cv2.imwrite(output_path, cropped_card)
            saved_paths.append(output_path)
            
            if debugging:
                print(f"  ✅ Saved: {output_path} ({cropped_card.shape[1]}x{cropped_card.shape[0]})")
    
    return saved_paths

def group_boxes_into_cards(boxes, image_width, image_height, debugging=False):
    """Group nearby text boxes into card regions"""
    
    if debugging:
        print(f"🔗 Grouping {len(boxes)} boxes into card regions...")
    
    # Calculate center points
    centers = []
    for box in boxes:
        x_coords = [point[0] for point in box]
        y_coords = [point[1] for point in box]
        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)
        centers.append((center_x, center_y))
    
    # Group boxes by proximity
    card_regions = []
    used_boxes = set()
    distance_threshold = min(image_width, image_height) * 0.25
    
    for i, (center_x, center_y) in enumerate(centers):
        if i in used_boxes:
            continue
        
        current_region = [boxes[i]]
        used_boxes.add(i)
        
        # Find nearby boxes
        for j, (other_x, other_y) in enumerate(centers):
            if j in used_boxes:
                continue
            
            distance = np.sqrt((center_x - other_x)**2 + (center_y - other_y)**2)
            
            if distance < distance_threshold:
                current_region.append(boxes[j])
                used_boxes.add(j)
        
        # Only keep regions with multiple text boxes
        if len(current_region) >= 2:
            card_regions.append(current_region)
    
    # Sort by size and take top 2
    card_regions.sort(key=lambda region: len(region), reverse=True)
    card_regions = card_regions[:2]
    
    if debugging:
        print(f"  Final card regions: {len(card_regions)}")
    
    return card_regions

# Simple test
if __name__ == "__main__":
    print("🎯 RELIABLE CARD DETECTOR")
    print("=" * 40)
    
    card_paths = crop_cards(debugging=True)
    
    if card_paths:
        print(f"\n✅ SUCCESS! Detected {len(card_paths)} cards:")
        for i, path in enumerate(card_paths, 1):
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024
                print(f"  📄 Card {i}: {path} ({file_size:.1f} KB)")
        
        print(f"\n📁 Cards saved to: reliable_cropped_cards/")
        print("🎉 This is the most reliable card detection solution!")
        
    else:
        print("❌ No cards detected")

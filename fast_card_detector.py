"""
Fast Card Detector using Google Vision API
Lightning-fast and reliable card detection
"""

import os
import cv2
from google.cloud import vision
import io

def crop_cards(image_path="test images/test4.jpg", 
               expand_top=50, expand_bottom=20, expand_left=20, expand_right=20,
               debugging=False):
    """
    Fast and reliable card detection using Google Vision API.
    
    Args:
        image_path: Path to image (default: "test images/test4.jpg")
        expand_top: % to expand upward (default: 50)
        expand_bottom: % to expand downward (default: 20) 
        expand_left: % to expand left (default: 20)
        expand_right: % to expand right (default: 20)
        debugging: Print debug info (default: False)
    
    Returns:
        List of cropped card image paths
    """
    
    # Check if credentials are set
    if not os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
        print("❌ Google Vision API credentials not found!")
        print("Please set GOOGLE_APPLICATION_CREDENTIALS environment variable")
        return []
    
    if debugging:
        print(f"🔍 Processing with Google Vision: {image_path}")
    
    try:
        # Initialize the client
        client = vision.ImageAnnotatorClient()
        
        # Load image
        with io.open(image_path, 'rb') as image_file:
            content = image_file.read()
        
        image = vision.Image(content=content)
        
        # Use text detection for fast and accurate results
        response = client.text_detection(image=image)
        
        if response.error.message:
            raise Exception(f'Google Vision API error: {response.error.message}')
        
        # Get text annotations
        texts = response.text_annotations
        
        if not texts:
            if debugging:
                print("❌ No text detected")
            return []
        
        # Load original image for cropping
        original_image = cv2.imread(image_path)
        height, width = original_image.shape[:2]
        
        if debugging:
            print(f"📏 Image dimensions: {width}x{height}")
            print(f"📝 Found {len(texts)} text annotations")
        
        # Skip the first annotation (full text) and process individual text blocks
        text_blocks = texts[1:]
        
        if not text_blocks:
            if debugging:
                print("❌ No individual text blocks found")
            return []
        
        # Extract bounding boxes from text blocks
        all_boxes = []
        for text in text_blocks:
            vertices = text.bounding_poly.vertices
            
            box = []
            for vertex in vertices:
                x = vertex.x if hasattr(vertex, 'x') else 0
                y = vertex.y if hasattr(vertex, 'y') else 0
                box.append([x, y])
            
            if len(box) >= 4:
                all_boxes.append(box)
        
        if debugging:
            print(f"🔗 Extracted {len(all_boxes)} valid bounding boxes")
        
        # Group boxes into card regions
        card_regions = group_boxes_into_cards(all_boxes, width, height, debugging)
        
        if not card_regions:
            if debugging:
                print("❌ Could not group boxes into card regions")
            return []
        
        # Create output directory
        os.makedirs("fast_cropped_cards", exist_ok=True)
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        saved_paths = []
        
        for i, region in enumerate(card_regions):
            if debugging:
                print(f"\n🔧 Processing card {i+1}...")
            
            # Get bounding rectangle of the region
            x_coords = [point[0] for box in region for point in box]
            y_coords = [point[1] for box in region for point in box]
            
            min_x, max_x = min(x_coords), max(x_coords)
            min_y, max_y = min(y_coords), max(y_coords)
            
            # Apply expansion
            card_width = max_x - min_x
            card_height = max_y - min_y
            
            expand_top_px = int((expand_top / 100.0) * card_height)
            expand_bottom_px = int((expand_bottom / 100.0) * card_height)
            expand_left_px = int((expand_left / 100.0) * card_width)
            expand_right_px = int((expand_right / 100.0) * card_width)
            
            # Calculate expanded bounds
            crop_x1 = max(0, min_x - expand_left_px)
            crop_y1 = max(0, min_y - expand_top_px)
            crop_x2 = min(width, max_x + expand_right_px)
            crop_y2 = min(height, max_y + expand_bottom_px)
            
            if debugging:
                print(f"  Card size: {card_width}x{card_height}")
                print(f"  Expansion: top={expand_top_px}px, bottom={expand_bottom_px}px, left={expand_left_px}px, right={expand_right_px}px")
                print(f"  Final bounds: ({crop_x1}, {crop_y1}) to ({crop_x2}, {crop_y2})")
            
            # Crop the card
            cropped_card = original_image[crop_y1:crop_y2, crop_x1:crop_x2]
            
            if cropped_card.size > 0:
                output_path = f"fast_cropped_cards/{base_name}_card_{i+1}.jpg"
                cv2.imwrite(output_path, cropped_card)
                saved_paths.append(output_path)
                
                if debugging:
                    print(f"  ✅ Saved: {output_path} ({cropped_card.shape[1]}x{cropped_card.shape[0]})")
        
        return saved_paths
        
    except Exception as e:
        print(f"❌ Error with Google Vision API: {str(e)}")
        if "google.cloud" in str(e):
            print("💡 Make sure you have installed: pip install google-cloud-vision")
        return []

def group_boxes_into_cards(boxes, image_width, image_height, debugging=False):
    """Group nearby text boxes into card regions"""
    
    if debugging:
        print(f"🔗 Grouping {len(boxes)} boxes into card regions...")
    
    # Calculate center points
    centers = []
    for box in boxes:
        x_coords = [point[0] for point in box]
        y_coords = [point[1] for point in box]
        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)
        centers.append((center_x, center_y))
    
    # Group boxes by proximity
    card_regions = []
    used_boxes = set()
    distance_threshold = min(image_width, image_height) * 0.25
    
    for i, (center_x, center_y) in enumerate(centers):
        if i in used_boxes:
            continue
        
        current_region = [boxes[i]]
        used_boxes.add(i)
        
        # Find nearby boxes
        for j, (other_x, other_y) in enumerate(centers):
            if j in used_boxes:
                continue
            
            distance = ((center_x - other_x)**2 + (center_y - other_y)**2)**0.5
            
            if distance < distance_threshold:
                current_region.append(boxes[j])
                used_boxes.add(j)
        
        # Only keep regions with multiple text boxes
        if len(current_region) >= 2:
            card_regions.append(current_region)
    
    # Sort by size and take top 2
    card_regions.sort(key=lambda region: len(region), reverse=True)
    card_regions = card_regions[:2]
    
    if debugging:
        print(f"  Final card regions: {len(card_regions)}")
    
    return card_regions

# Simple test
if __name__ == "__main__":
    print("⚡ FAST CARD DETECTOR (Google Vision)")
    print("=" * 45)
    
    card_paths = crop_cards(debugging=True)
    
    if card_paths:
        print(f"\n✅ SUCCESS! Detected {len(card_paths)} cards:")
        for i, path in enumerate(card_paths, 1):
            if os.path.exists(path):
                file_size = os.path.getsize(path) / 1024
                print(f"  📄 Card {i}: {path} ({file_size:.1f} KB)")
        
        print(f"\n📁 Cards saved to: fast_cropped_cards/")
        print("⚡ Lightning fast with Google Vision API!")
        
    else:
        print("❌ No cards detected")
